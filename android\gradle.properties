org.gradle.jvmargs=-Xmx3072m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED --add-opens=java.desktop/java.awt.font=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
kotlin.incremental=false
kotlin.incremental.android=false
android.useAndroidX=true
android.enableJetifier=true
# android.enableR8=true (removed as it's deprecated)
kotlin.jvm.target.validation.mode = IGNORE
# Suppress unsupported compileSdk warning
android.suppressUnsupportedCompileSdk=34
# Set Java 21 home path (using your installed Java 21)
org.gradle.java.home=C:/Program Files/Java/jdk-21
# Set Gradle version
org.gradle.wrapper.version=8.14
# Disable JDK image transformation to fix Java 24 compatibility
android.experimental.enableJdkImageTransform=false
# Additional Java 21 compatibility settings
android.experimental.enableJdkImageTransform=false
android.experimental.enableJdkImageTransform.enabled=false