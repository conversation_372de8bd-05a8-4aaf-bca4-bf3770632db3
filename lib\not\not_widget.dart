import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/services/notification_service.dart';
import '/utils/simple_localization.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'not_model.dart';
export 'not_model.dart';

class NotWidget extends StatefulWidget {
  const NotWidget({super.key});

  static String routeName = 'not';
  static String routePath = '/not';

  @override
  State<NotWidget> createState() => _NotWidgetState();
}

class _NotWidgetState extends State<NotWidget> {
  late NotModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  // Helper method to get notification title based on type
  String getNotificationTitle(NotificationType type, BuildContext context) {
    switch (type) {
      case NotificationType.order:
        return context.tr('orderUpdate', 'Order Update');
      case NotificationType.payment:
        return context.tr('payment', 'Payment');
      case NotificationType.promotion:
        return context.tr('promotion', 'Promotion');
      case NotificationType.general:
        return context.tr('general', 'General');
    }
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => NotModel());
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    await _model.loadNotifications();
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
          automaticallyImplyLeading: false,
          leading: FlutterFlowIconButton(
            borderRadius: 8.0,
            buttonSize: 40.0,
            icon: Icon(
              Icons.arrow_back_ios_rounded,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 24.0,
            ),
            onPressed: () {
              context.pop();
            },
          ),
          title: Text(
            context.tr('notifications', 'Notifications'),
            style: FlutterFlowTheme.of(context).headlineMedium.override(
                  font: GoogleFonts.interTight(
                    fontWeight: FontWeight.bold,
                  ),
                ),
          ),
          actions: const [],
          centerTitle: false,
          elevation: 0.0,
        ),
        body: SafeArea(
          child: Column(
            children: [
              // Notifications list
              Expanded(
                child: StatefulBuilder(
                  builder: (context, setState) {
                    if (_model.isLoading) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (_model.notifications.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.notifications_off_outlined,
                              size: 60,
                              color: FlutterFlowTheme.of(context).secondaryText,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              context.tr(
                                  'noNotificationsYet', 'No notifications yet'),
                              style: FlutterFlowTheme.of(context).titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              context.tr('youllSeeNotificationsHere',
                                  'You\'ll see your notifications here'),
                              style: FlutterFlowTheme.of(context).bodyMedium,
                            ),
                          ],
                        ),
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        await _model.loadNotifications();
                        setState(() {});
                      },
                      child: ListView.builder(
                        padding: const EdgeInsets.only(top: 8),
                        itemCount: _model.notifications.length,
                        itemBuilder: (context, index) {
                          final notification = _model.notifications[index];
                          final isRead = notification.isRead;

                          // Determine color based on notification type and read status
                          Color indicatorColor;
                          if (!isRead) {
                            indicatorColor =
                                FlutterFlowTheme.of(context).primary;
                          } else {
                            indicatorColor =
                                FlutterFlowTheme.of(context).alternate;
                          }

                          return Dismissible(
                            key: Key(notification.id),
                            direction: DismissDirection.endToStart,
                            background: Container(
                              color: FlutterFlowTheme.of(context).error,
                              alignment: Alignment.centerRight,
                              padding: const EdgeInsets.only(right: 20),
                              child: const Icon(
                                Icons.delete,
                                color: Colors.white,
                              ),
                            ),
                            onDismissed: (direction) {
                              final notificationId = notification.id;

                              // Show snackbar before the async operation
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: const Text('Notification deleted'),
                                  action: SnackBarAction(
                                    label: 'UNDO',
                                    onPressed: () {
                                      // This is just a placeholder - in a real app you'd implement undo
                                      _model.loadNotifications().then((_) {
                                        if (mounted) {
                                          setState(() {});
                                        }
                                      });
                                    },
                                  ),
                                ),
                              );

                              // Delete the notification
                              _model
                                  .deleteNotification(notificationId)
                                  .then((_) {
                                if (mounted) {
                                  setState(() {});
                                }
                              });
                            },
                            child: InkWell(
                              onTap: () {
                                // Store notification data locally
                                final notificationId = notification.id;
                                final notificationType = notification.type;
                                final notificationMessage =
                                    notification.message;

                                // Mark as read and show details
                                if (!isRead) {
                                  _model.markAsRead(notificationId).then((_) {
                                    setState(() {});
                                  });
                                }

                                // Show notification details
                                showDialog(
                                  context: context,
                                  builder: (dialogContext) => AlertDialog(
                                    title: Text(
                                      getNotificationTitle(
                                          notificationType, context),
                                      style: FlutterFlowTheme.of(dialogContext)
                                          .titleMedium,
                                    ),
                                    content: Text(notificationMessage),
                                    actions: [
                                      TextButton(
                                        onPressed: () =>
                                            Navigator.pop(dialogContext),
                                        child:
                                            Text(context.tr('close', 'Close')),
                                      ),
                                    ],
                                  ),
                                );
                              },
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 1),
                                child: Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: isRead
                                        ? FlutterFlowTheme.of(context)
                                            .primaryBackground
                                        : FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                    boxShadow: const [
                                      BoxShadow(
                                        blurRadius: 0.0,
                                        color: Color(0xFFE0E3E7),
                                        offset: Offset(0.0, 1.0),
                                      )
                                    ],
                                    borderRadius: BorderRadius.circular(0.0),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12.0),
                                    child: Row(
                                      children: [
                                        // Indicator bar
                                        Container(
                                          width: 4.0,
                                          height: 50.0,
                                          decoration: BoxDecoration(
                                            color: indicatorColor,
                                            borderRadius:
                                                BorderRadius.circular(2.0),
                                          ),
                                        ),

                                        // Notification content
                                        Expanded(
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                left: 12.0),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                // Notification type indicator
                                                Text(
                                                  getNotificationTitle(
                                                      notification.type,
                                                      context),
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .labelSmall
                                                      .copyWith(
                                                        color:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .primary,
                                                      ),
                                                ),
                                                const SizedBox(height: 4),

                                                // Notification message
                                                Text(
                                                  notification.message,
                                                  style: isRead
                                                      ? FlutterFlowTheme.of(
                                                              context)
                                                          .labelLarge
                                                      : FlutterFlowTheme.of(
                                                              context)
                                                          .bodyLarge,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),

                                        // Date
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(left: 12.0),
                                          child: Text(
                                            notification.date,
                                            style: FlutterFlowTheme.of(context)
                                                .labelMedium,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
