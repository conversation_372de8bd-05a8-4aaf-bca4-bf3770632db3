name: dropshipping_dinal
description: A new Flutter project.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  auto_size_text: ^3.0.0
  cached_network_image: ^3.3.0
  cached_network_image_platform_interface: 4.1.1
  cached_network_image_web: 1.3.1
  collection: ^1.17.0
  crypto: ^3.0.6
  firebase_auth: ^5.5.4
  firebase_auth_platform_interface: ^7.6.3
  firebase_auth_web: ^5.14.3
  firebase_core: ^3.13.1
  firebase_core_platform_interface: ^5.4.0
  firebase_core_web: ^2.23.0
  firebase_performance: ^0.10.1+6
  firebase_performance_platform_interface: ^0.1.5+6
  firebase_performance_web: ^0.1.7+12
  flutter_animate: ^4.3.0
  flutter_cache_manager: 3.4.1
  font_awesome_flutter: ^10.8.0
  from_css_color: ^2.0.0
  go_router: ^15.1.2
  google_fonts: ^6.1.0
  google_sign_in: ^6.3.0
  google_sign_in_android: ^6.2.1
  google_sign_in_ios: ^5.9.0
  google_sign_in_platform_interface: ^2.5.0
  google_sign_in_web: ^0.12.4+4
  intl: ^0.19.0
  json_path: ^0.7.0
  page_transition: ^2.2.0
  path_provider: ^2.1.5
  path_provider_android: ^2.2.17
  path_provider_foundation: ^2.4.1
  path_provider_linux: 2.2.1
  path_provider_platform_interface: 2.1.2
  path_provider_windows: 2.3.0
  plugin_platform_interface: 2.1.8
  provider: ^6.1.1
  rxdart: ^0.28.0
  shared_preferences: ^2.2.2
  shared_preferences_android: ^2.4.10
  shared_preferences_foundation: ^2.5.4
  shared_preferences_linux: 2.4.1
  shared_preferences_platform_interface: 2.4.1
  shared_preferences_web: ^2.4.3
  shared_preferences_windows: 2.4.1
  sign_in_with_apple: ^7.0.1
  sign_in_with_apple_platform_interface: ^2.0.0
  sign_in_with_apple_web: ^3.0.0
  sqflite: ^2.3.0
  sqflite_common: ^2.5.4
  timeago: ^3.7.1
  url_launcher: ^6.3.0
  url_launcher_android: ^6.3.16
  url_launcher_ios: ^6.3.3
  url_launcher_linux: ^3.2.1
  url_launcher_macos: ^3.2.2
  url_launcher_platform_interface: 2.3.2
  url_launcher_web: ^2.4.1
  url_launcher_windows: ^3.1.4
  cupertino_icons: ^1.0.6
  cloud_firestore: ^5.6.8
  cloud_firestore_web: ^4.3.4
  cloud_firestore_platform_interface: ^6.0.0
  firebase_ai: ^2.0.0
  firebase_messaging: ^15.1.6
  flutter_local_notifications: ^18.0.1
  http: ^1.2.2
  shimmer: ^3.0.0

dev_dependencies:
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter

flutter:
  generate: true
  uses-material-design: true
  assets:
    - assets/fonts/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/lottie_animations/
    - assets/rive_animations/
    - assets/pdfs/
    - assets/jsons/

flutter_gen:
  output: lib/generated/
  line_length: 80
  synthetic_package: false
  integrations:
    flutter_svg: false
    flare_flutter: false
    rive: false
    lottie: false