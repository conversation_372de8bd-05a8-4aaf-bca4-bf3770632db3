import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/locale_service.dart';

/// Simple localization without complex imports
class SimpleL10n {
  static final Map<String, Map<String, String>> _translations = {
    'en': {
      'searchProducts': 'Search for products...',
      'store': 'Store',
      'cash': 'Cash',
      'orders': 'Orders',
      'account': 'Account',
      'categories': 'Categories',
      'viewAll': 'View All',
      'electronics': 'Electronics',
      'fashion': 'Fashion',
      'home': 'Home',
      'sports': 'Sports',
      'mostSelling': 'Most Selling',
      'popularProducts': 'Popular Products',
      'unableToLoadMostSelling': 'Unable to load Most Selling products',
      'loadingMostSelling': 'Loading Most Selling products...',
      'unableToLoadPopular': 'Unable to load popular products',
      'loadingPopular': 'Loading popular products...',
      // Settings page translations
      'settingsPage': 'Settings Page',
      'pleaseEvaluateOptions': 'Please evaluate your options below.',
      'gettingStarted': 'Getting Started',
      'aboutUs': 'About Us',
      'help': 'Help',
      'privacyPolicy': 'Privacy Policy',
      'termsConditions': 'Terms & Conditions',
      'notificationSetting': 'Notification setting',
      'darkMode': 'Dark Mode',
      'darkModeEnabled': 'Dark mode enabled',
      'lightModeEnabled': 'Light mode enabled',
      'aiAssistantDemo': 'AI Assistant Demo',
      'followUsOn': 'Follow us on',
      'appVersions': 'App Versions',
      'version': 'v0.0.1',
      'logOut': 'Log Out',
      // Withdraw page translations
      'withdraw': 'Withdraw',
      'withdrawAmount': 'Withdraw Amount',
      'withdrawTo': 'Withdraw To',
      'withdrawalToWallet': 'withdrawal to your wallet',
      'enterZainCashNumber': 'Enter your Zain Cash number',
      'ensureNumberRegistered':
          'Please ensure the number is registered with Zain Cash',
      'pleaseEnterAmount': 'Please enter an amount to withdraw',
      'pleaseEnterValidAmount': 'Please enter a valid amount',
      'pleaseEnterZainCashNumber': 'Please enter your Zain Cash number',
      'withdrawNow': 'Withdraw Now',
      'withdrawalRequestSubmitted':
          'Withdrawal request of \$\${amount} submitted successfully. You will receive the payment shortly.',
      'withdrawalRequestFailed':
          'Failed to submit withdrawal request. Please try again.',
      'balanceUpdatedSuccessfully': 'Balance updated successfully!',
      'errorUpdatingBalance': 'Error updating balance: \${error}',
      // Shopping cart translations
      'shoppingCart': 'Shopping Cart',
      'addItemsToCart': 'Add items to your cart to see them here',
      'totalItems': 'Total Items:',
      'totalAmount': 'Total Amount:',
      'totalEarnings': 'Total Earnings:',
      // Orders page translations
      'errorLoadingOrders': 'Error loading orders: \${error}',
      'noOrdersFound': 'No orders found',
      'orderNumber': 'Order #\${orderNumber}',
      'total': 'Total: \$\${amount}',
      // Additional withdraw page translations
      'incomingEarningsPending':
          'Incoming Earnings (Pending Admin Confirmation)',
      'earningsAvailableAfterConfirmation':
          'Earnings will be available for withdrawal after order confirmation',
      'availableBalanceWithdrawable': 'Available Balance (Withdrawable)',
      'insufficientBalance': 'Insufficient balance',
      // Account page translations (new keys only)
      'personalInformation': 'Personal Information',
      'paymentsAndWithdraw': 'Payments and withdraw',
      'helpCenter': 'Help Center',
      'contactUs': 'Contact us',
      'member': 'Member',
      'user': 'User',
      'noEmail': 'No email',
      // Product page translations
      'productDetails': 'Product Details',
      'downloadImage': 'Download Image',
      'mainPrice': 'Main Price',
      'minPrice': 'Min price',
      'maxPrice': 'Max price',
      'selectYourPrice': 'Select Your Price',
      'choosePriceBetween': 'Choose a price between',
      'enterPrice': 'Enter Price',
      'currentPrice': 'Current price',
      'pleaseEnterPrice': 'Please enter a price',
      'pleaseEnterValidNumber': 'Please enter a valid number',
      'priceMustBeBetween': 'Price must be between',
      'addToCart': 'Add to Cart',
      'itemAddedToCart': 'Item added to cart',
      'unknownProduct': 'Unknown Product',
      'noDescriptionAvailable': 'No description available',
      'unknownBrand': 'Unknown Brand',
      // Login page translations
      'signIn': 'Sign In',
      'signUp': 'Sign Up',
      'letsGetStarted': 'Let\'s get started by filling out the form below.',
      'email': 'Email',
      'password': 'Password',
      'confirmPassword': 'Confirm Password',
      'pleaseEnterEmail': 'Please enter your email',
      'pleaseEnterPassword': 'Please enter your password',
      'signInFailed': 'Sign in failed. Please try again.',
      'signInSuccessful': 'Sign in successful!',
      'passwordsDontMatch': 'Passwords don\'t match!',
      'createAccount': 'Create Account',
      'forgotPassword': 'Forgot Password',
      'forgotPasswordMessage':
          'Forgot password functionality will be implemented',
      'orSignUpWith': 'Or sign up with',
      'continueWithGoogle': 'Continue with Google',
      'continueWithApple': 'Continue with Apple',
      // Favorites page translations
      'favorites': 'Favorites',
      'yourFavoriteItems': 'Your Favorite Items',
      'items': 'items',
      'noFavoritesYet': 'No favorites yet',
      'itemsYouMarkFavorites': 'Items you mark as favorites will appear here',
      'exploreProducts': 'Explore Products',
      'addedToCart': 'Added to cart',
      // Search page translations
      'searchResultsFor': 'Search results for',
      'searchForProducts': 'Search for products',
      'enterProductDescription':
          'Enter a product name, category, or description',
      'noResultsFound': 'No results found',
      'tryDifferentKeywords': 'Try searching with different keywords',
      'somethingWentWrong': 'Something went wrong',
      'pleaseTryAgainLater': 'Please try again later',
      'result': 'result',
      'results': 'results',
      'found': 'found',
      'flash': 'FLASH',
      'featured': 'FEATURED',
      // AI Demo page translations
      'aiDemo': 'AI Demo',
      'productDescriptionGenerator': 'Product Description Generator',
      'productName': 'Product Name',
      'enterProductName': 'Enter product name...',
      'features': 'Features (Optional)',
      'enterProductFeatures': 'Enter product features...',
      'generateDescription': 'Generate Description',
      'generatedDescription': 'Generated Description',
      'marketingCopyGenerator': 'Marketing Copy Generator',
      'generateMarketingCopy': 'Generate Marketing Copy',
      'generatedMarketingCopy': 'Generated Marketing Copy',
      'customerSupportAssistant': 'Customer Support Assistant',
      'customerQuery': 'Customer Query',
      'enterCustomerQuestion': 'Enter customer question...',
      'generateSupportResponse': 'Generate Support Response',
      'generatedSupportResponse': 'Generated Support Response',
      'pleaseEnterProductName': 'Please enter a product name',
      'errorGeneratingDescription': 'Error generating description',
      'errorGeneratingMarketingCopy': 'Error generating marketing copy',
      'pleaseEnterCustomerQuery': 'Please enter a customer query',
      'errorGeneratingSupportResponse': 'Error generating support response',
      // Firebase Setup page translations
      'firebaseSetup': 'Firebase Setup',
      'firebaseDatabaseSetup': 'Firebase Database Setup',
      'firebaseSetupDescription':
          'This page allows you to initialize your Firebase database with sample data for testing purposes.',
      'databaseStatus': 'Database Status',
      'firebaseNotConfigured': 'Firebase is not properly configured',
      'checkFirebaseSetup':
          'Please check your Firebase setup and make sure you have the correct configuration.',
      'products': 'Products',
      'users': 'Users',
      'refreshStatus': 'Refresh Status',
      'initializing': 'Initializing...',
      'initializeFirebaseData': 'Initialize Firebase Data',
      'noteEmptyCollections':
          'Note: This will only add data if your collections are empty.',
      'checkingFirebaseConfiguration': 'Checking Firebase configuration...',
      'initializingFirebaseData': 'Initializing Firebase data...',
      'firebaseDataInitializedSuccessfully':
          'Firebase data initialized successfully!',
      'errorInitializingFirebaseData': 'Error initializing Firebase data',
      'errorCheckingDatabaseStatus': 'Error checking database status',

      // All items page translations
      'allProducts': 'All Products',
      'addedToFavorites': 'Added to favorites',
      'removedFromFavorites': 'Removed from favorites',

      // Notification page translations
      'notifications': 'Notifications',
      'noNotificationsYet': 'No notifications yet',
      'youllSeeNotificationsHere': 'You\'ll see your notifications here',
      'orderUpdate': 'Order Update',
      'payment': 'Payment',
      'promotion': 'Promotion',
      'general': 'General',
      'close': 'Close',

      // Notification settings page translations
      'notificationSettings': 'Notification Settings',
      'pushNotifications': 'Push Notifications',
      'emailNotifications': 'Email Notifications',
      'receiveNotifications': 'Receive notifications on your device',
      'receiveEmailNotifications': 'Receive notifications via email',
      'chooseNotificationSettings':
          'Choose what notifications you want to receive below and we will update the settings.',
      'pushNotificationsEnabled': 'Push notifications enabled',
      'pushNotificationsDisabled': 'Push notifications disabled',
      'saveChanges': 'Save Changes',

      // Flash Sale / Most Selling page translations
      'trendingNow': 'Trending Now',

      // Customer Information page translations
      'customerInformation': 'Customer Information',
      'contactDetails': 'Contact Details',
      'customerName': 'Customer Name',
      'name': 'Name',
      'phoneNumber': 'Phone Number',
      'enterPhoneNumber': 'Enter phone number',
      'city': 'City',
      'enterCity': 'Enter city',
      'notes': 'Notes',
      'addAdditionalInformation': 'Add any additional information here...',
      'completeOrder': 'Complete Order',
      'pleaseEnterName': 'Please enter your name',
      'pleaseEnterPhoneNumber': 'Please enter your phone number',
      'pleaseEnterCity': 'Please enter your city',
      'pleaseSignInFirst': 'Please sign in first to place orders!',
      'errorPlacingOrder': 'Error placing order',
      'orderPlacedSuccessfully': 'Order placed successfully!',
      'addedToEarnings': 'added to your incoming earnings.',
      'userNotAuthenticated': 'User not authenticated',

      // Shopping Cart page translations
      'yourCartIsEmpty': 'Your cart is empty',
      'continueShopping': 'Continue Shopping',
      'color': 'Color',
      'price': 'Price',
      'earnings': 'Earnings',
      'qty': 'Qty',
      'errorUpdatingQuantity': 'Error updating quantity',
      'errorRemovingItem': 'Error removing item',
      'orderSummary': 'Order Summary',
      'proceedToCheckout': 'Proceed to Checkout',

      // Account page translations (additional keys)
      'termsAndConditions': 'Terms & Conditions',
      'privacyPolicy': 'Privacy Policy',

      // Orders page translations (additional keys)
      'myOrders': 'My Orders',
      'placedOn': 'Placed on',
      'pending': 'Pending',
      'confirmed': 'Confirmed',
      'delivered': 'Delivered',
      'cancelled': 'Cancelled',
    },
    'ar': {
      'searchProducts': 'البحث عن المنتجات...',
      'store': 'المتجر',
      'cash': 'نقد',
      'orders': 'الطلبات',
      'account': 'الحساب',
      'categories': 'الفئات',
      'viewAll': 'عرض الكل',
      'electronics': 'الإلكترونيات',
      'fashion': 'الأزياء',
      'home': 'المنزل',
      'sports': 'الرياضة',
      'mostSelling': 'الأكثر مبيعاً',
      'popularProducts': 'المنتجات الشائعة',
      'unableToLoadMostSelling': 'غير قادر على تحميل المنتجات الأكثر مبيعاً',
      'loadingMostSelling': 'جاري تحميل المنتجات الأكثر مبيعاً...',
      'unableToLoadPopular': 'غير قادر على تحميل المنتجات الشائعة',
      'loadingPopular': 'جاري تحميل المنتجات الشائعة...',
      // Settings page translations
      'settingsPage': 'صفحة الإعدادات',
      'pleaseEvaluateOptions': 'يرجى تقييم الخيارات أدناه.',
      'gettingStarted': 'البدء',
      'aboutUs': 'عنا',
      'help': 'مساعدة',
      'privacyPolicy': 'سياسة الخصوصية',
      'termsConditions': 'الشروط والأحكام',
      'notificationSetting': 'إعدادات الإشعارات',
      'darkMode': 'الوضع المظلم',
      'darkModeEnabled': 'تم تفعيل الوضع المظلم',
      'lightModeEnabled': 'تم تفعيل الوضع الفاتح',
      'aiAssistantDemo': 'عرض المساعد الذكي',
      'followUsOn': 'تابعنا على',
      'appVersions': 'إصدارات التطبيق',
      'version': 'v0.0.1',
      'logOut': 'تسجيل الخروج',
      // Withdraw page translations
      'withdraw': 'سحب',
      'withdrawAmount': 'مبلغ السحب',
      'withdrawTo': 'السحب إلى',
      'withdrawalToWallet': 'السحب إلى محفظتك',
      'enterZainCashNumber': 'أدخل رقم زين كاش الخاص بك',
      'ensureNumberRegistered': 'يرجى التأكد من أن الرقم مسجل في زين كاش',
      'pleaseEnterAmount': 'يرجى إدخال مبلغ للسحب',
      'pleaseEnterValidAmount': 'يرجى إدخال مبلغ صحيح',
      'pleaseEnterZainCashNumber': 'يرجى إدخال رقم زين كاش الخاص بك',
      'withdrawNow': 'اسحب الآن',
      'withdrawalRequestSubmitted':
          'تم تقديم طلب السحب بمبلغ \$\${amount} بنجاح. ستتلقى الدفعة قريباً.',
      'withdrawalRequestFailed':
          'فشل في تقديم طلب السحب. يرجى المحاولة مرة أخرى.',
      'balanceUpdatedSuccessfully': 'تم تحديث الرصيد بنجاح!',
      'errorUpdatingBalance': 'خطأ في تحديث الرصيد: \${error}',
      // Shopping cart translations
      'shoppingCart': 'عربة التسوق',
      'addItemsToCart': 'أضف عناصر إلى عربة التسوق لرؤيتها هنا',
      'totalItems': 'إجمالي العناصر:',
      'totalAmount': 'المبلغ الإجمالي:',
      'totalEarnings': 'إجمالي الأرباح:',
      // Orders page translations
      'errorLoadingOrders': 'خطأ في تحميل الطلبات: \${error}',
      'noOrdersFound': 'لم يتم العثور على طلبات',
      'orderNumber': 'طلب #\${orderNumber}',
      'total': 'الإجمالي: \$\${amount}',
      // Additional withdraw page translations
      'incomingEarningsPending': 'الأرباح الواردة (في انتظار تأكيد المشرف)',
      'earningsAvailableAfterConfirmation':
          'ستكون الأرباح متاحة للسحب بعد تأكيد الطلب',
      'availableBalanceWithdrawable': 'الرصيد المتاح (قابل للسحب)',
      'insufficientBalance': 'رصيد غير كافي',
      // Account page translations (new keys only)
      'personalInformation': 'المعلومات الشخصية',
      'paymentsAndWithdraw': 'المدفوعات والسحب',
      'helpCenter': 'مركز المساعدة',
      'contactUs': 'اتصل بنا',
      'member': 'عضو',
      'user': 'مستخدم',
      'noEmail': 'لا يوجد بريد إلكتروني',
      'description': 'الوصف',
      // Product page translations
      'productDetails': 'تفاصيل المنتج',
      'downloadImage': 'تحميل الصورة',
      'mainPrice': 'السعر الأساسي',
      'minPrice': 'أقل سعر',
      'maxPrice': 'أعلى سعر',
      'selectYourPrice': 'اختر سعرك',
      'choosePriceBetween': 'اختر سعراً بين',
      'enterPrice': 'أدخل السعر',
      'currentPrice': 'السعر الحالي',
      'pleaseEnterPrice': 'يرجى إدخال سعر',
      'pleaseEnterValidNumber': 'يرجى إدخال رقم صحيح',
      'priceMustBeBetween': 'يجب أن يكون السعر بين',
      'addToCart': 'أضف إلى السلة',
      'itemAddedToCart': 'تم إضافة العنصر إلى السلة',
      'unknownProduct': 'منتج غير معروف',
      'noDescriptionAvailable': 'لا يوجد وصف متاح',
      'unknownBrand': 'علامة تجارية غير معروفة',
      // Login page translations
      'signIn': 'تسجيل الدخول',
      'signUp': 'إنشاء حساب',
      'letsGetStarted': 'لنبدأ بملء النموذج أدناه.',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
      'confirmPassword': 'تأكيد كلمة المرور',
      'pleaseEnterEmail': 'يرجى إدخال بريدك الإلكتروني',
      'pleaseEnterPassword': 'يرجى إدخال كلمة المرور',
      'signInFailed': 'فشل تسجيل الدخول. يرجى المحاولة مرة أخرى.',
      'signInSuccessful': 'تم تسجيل الدخول بنجاح!',
      'passwordsDontMatch': 'كلمات المرور غير متطابقة!',
      'createAccount': 'إنشاء حساب',
      'forgotPassword': 'نسيت كلمة المرور',
      'forgotPasswordMessage': 'سيتم تنفيذ وظيفة نسيان كلمة المرور',
      'orSignUpWith': 'أو سجل باستخدام',
      'continueWithGoogle': 'المتابعة مع جوجل',
      'continueWithApple': 'المتابعة مع آبل',
      // Favorites page translations
      'favorites': 'المفضلة',
      'yourFavoriteItems': 'عناصرك المفضلة',
      'items': 'عناصر',
      'noFavoritesYet': 'لا توجد مفضلة بعد',
      'itemsYouMarkFavorites': 'العناصر التي تضعها في المفضلة ستظهر هنا',
      'exploreProducts': 'استكشف المنتجات',
      'addedToCart': 'تم إضافته إلى السلة',
      // Search page translations
      'searchResultsFor': 'نتائج البحث عن',
      'searchForProducts': 'البحث عن المنتجات',
      'enterProductDescription': 'أدخل اسم المنتج أو الفئة أو الوصف',
      'noResultsFound': 'لم يتم العثور على نتائج',
      'tryDifferentKeywords': 'جرب البحث بكلمات مفتاحية مختلفة',
      'somethingWentWrong': 'حدث خطأ ما',
      'pleaseTryAgainLater': 'يرجى المحاولة مرة أخرى لاحقاً',
      'result': 'نتيجة',
      'results': 'نتائج',
      'found': 'موجود',
      'flash': 'عرض خاطف',
      'featured': 'مميز',
      // AI Demo page translations
      'aiDemo': 'عرض الذكاء الاصطناعي',
      'productDescriptionGenerator': 'مولد وصف المنتج',
      'productName': 'اسم المنتج',
      'enterProductName': 'أدخل اسم المنتج...',
      'features': 'الميزات (اختياري)',
      'enterProductFeatures': 'أدخل ميزات المنتج...',
      'generateDescription': 'إنشاء الوصف',
      'generatedDescription': 'الوصف المُنشأ',
      'marketingCopyGenerator': 'مولد المحتوى التسويقي',
      'generateMarketingCopy': 'إنشاء محتوى تسويقي',
      'generatedMarketingCopy': 'المحتوى التسويقي المُنشأ',
      'customerSupportAssistant': 'مساعد دعم العملاء',
      'customerQuery': 'استفسار العميل',
      'enterCustomerQuestion': 'أدخل سؤال العميل...',
      'generateSupportResponse': 'إنشاء رد الدعم',
      'generatedSupportResponse': 'رد الدعم المُنشأ',
      'pleaseEnterProductName': 'يرجى إدخال اسم المنتج',
      'errorGeneratingDescription': 'خطأ في إنشاء الوصف',
      'errorGeneratingMarketingCopy': 'خطأ في إنشاء المحتوى التسويقي',
      'pleaseEnterCustomerQuery': 'يرجى إدخال استفسار العميل',
      'errorGeneratingSupportResponse': 'خطأ في إنشاء رد الدعم',
      // Firebase Setup page translations
      'firebaseSetup': 'إعداد Firebase',
      'firebaseDatabaseSetup': 'إعداد قاعدة بيانات Firebase',
      'firebaseSetupDescription':
          'تتيح لك هذه الصفحة تهيئة قاعدة بيانات Firebase الخاصة بك ببيانات تجريبية لأغراض الاختبار.',
      'databaseStatus': 'حالة قاعدة البيانات',
      'firebaseNotConfigured': 'Firebase غير مُكوّن بشكل صحيح',
      'checkFirebaseSetup':
          'يرجى التحقق من إعداد Firebase والتأكد من أن لديك التكوين الصحيح.',
      'products': 'المنتجات',
      'users': 'المستخدمون',
      'refreshStatus': 'تحديث الحالة',
      'initializing': 'جاري التهيئة...',
      'initializeFirebaseData': 'تهيئة بيانات Firebase',
      'noteEmptyCollections':
          'ملاحظة: سيتم إضافة البيانات فقط إذا كانت مجموعاتك فارغة.',
      'checkingFirebaseConfiguration': 'جاري التحقق من تكوين Firebase...',
      'initializingFirebaseData': 'جاري تهيئة بيانات Firebase...',
      'firebaseDataInitializedSuccessfully': 'تم تهيئة بيانات Firebase بنجاح!',
      'errorInitializingFirebaseData': 'خطأ في تهيئة بيانات Firebase',
      'errorCheckingDatabaseStatus': 'خطأ في التحقق من حالة قاعدة البيانات',

      // All items page translations
      'allProducts': 'جميع المنتجات',
      'addedToFavorites': 'تم إضافته إلى المفضلة',
      'removedFromFavorites': 'تم إزالته من المفضلة',

      // Notification page translations
      'notifications': 'الإشعارات',
      'noNotificationsYet': 'لا توجد إشعارات بعد',
      'youllSeeNotificationsHere': 'ستظهر إشعاراتك هنا',
      'orderUpdate': 'تحديث الطلب',
      'payment': 'الدفع',
      'promotion': 'العرض الترويجي',
      'general': 'عام',
      'close': 'إغلاق',

      // Notification settings page translations
      'notificationSettings': 'إعدادات الإشعارات',
      'pushNotifications': 'الإشعارات الفورية',
      'emailNotifications': 'إشعارات البريد الإلكتروني',
      'receiveNotifications': 'تلقي الإشعارات على جهازك',
      'receiveEmailNotifications': 'تلقي الإشعارات عبر البريد الإلكتروني',
      'chooseNotificationSettings':
          'اختر الإشعارات التي تريد تلقيها أدناه وسنقوم بتحديث الإعدادات.',
      'pushNotificationsEnabled': 'تم تفعيل الإشعارات الفورية',
      'pushNotificationsDisabled': 'تم إلغاء تفعيل الإشعارات الفورية',
      'saveChanges': 'حفظ التغييرات',

      // Flash Sale / Most Selling page translations
      'trendingNow': 'الأكثر رواجاً الآن',

      // Customer Information page translations
      'customerInformation': 'معلومات العميل',
      'contactDetails': 'تفاصيل الاتصال',
      'customerName': 'اسم العميل',
      'name': 'الاسم',
      'phoneNumber': 'رقم الهاتف',
      'enterPhoneNumber': 'أدخل رقم الهاتف',
      'city': 'المدينة',
      'enterCity': 'أدخل المدينة',
      'notes': 'ملاحظات',
      'addAdditionalInformation': 'أضف أي معلومات إضافية هنا...',
      'completeOrder': 'إتمام الطلب',
      'pleaseEnterName': 'يرجى إدخال اسمك',
      'pleaseEnterPhoneNumber': 'يرجى إدخال رقم هاتفك',
      'pleaseEnterCity': 'يرجى إدخال مدينتك',
      'pleaseSignInFirst': 'يرجى تسجيل الدخول أولاً لتقديم الطلبات!',
      'errorPlacingOrder': 'خطأ في تقديم الطلب',
      'orderPlacedSuccessfully': 'تم تقديم الطلب بنجاح!',
      'addedToEarnings': 'تم إضافته إلى أرباحك الواردة.',
      'userNotAuthenticated': 'المستخدم غير مصادق عليه',

      // Shopping Cart page translations
      'yourCartIsEmpty': 'سلة التسوق فارغة',
      'continueShopping': 'متابعة التسوق',
      'color': 'اللون',
      'price': 'السعر',
      'earnings': 'الأرباح',
      'qty': 'الكمية',
      'errorUpdatingQuantity': 'خطأ في تحديث الكمية',
      'errorRemovingItem': 'خطأ في إزالة العنصر',
      'orderSummary': 'ملخص الطلب',
      'proceedToCheckout': 'المتابعة للدفع',

      // Account page translations (additional keys)
      'termsAndConditions': 'الشروط والأحكام',

      // Orders page translations (additional keys)
      'myOrders': 'طلباتي',
      'placedOn': 'تم الطلب في',
      'pending': 'في الانتظار',
      'confirmed': 'مؤكد',
      'delivered': 'تم التسليم',
      'cancelled': 'ملغي',
    },
  };

  static String tr(BuildContext context, String key, [String? fallback]) {
    // Get the current locale from LocaleService instead of system locale
    final localeService = Provider.of<LocaleService>(context, listen: false);
    final locale = localeService.locale.languageCode;
    print(
        '🌍 SimpleL10n.tr: key="$key", locale="$locale", translation="${_translations[locale]?[key]}", fallback="$fallback"');
    return _translations[locale]?[key] ?? fallback ?? key;
  }
}

extension SimpleLocalizationExtension on BuildContext {
  String tr(String key, [String? fallback]) {
    return SimpleL10n.tr(this, key, fallback);
  }
}
