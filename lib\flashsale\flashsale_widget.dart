import '/dto/product_dto.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import '/services/favorites_service.dart';
import '/services/enhanced_image_service.dart';
import '/utils/simple_localization.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'flashsale_model.dart';
export 'flashsale_model.dart';

class FlashsaleWidget extends StatefulWidget {
  const FlashsaleWidget({super.key});

  static String routeName = 'flashsale';
  static String routePath = '/flashsale';

  @override
  State<FlashsaleWidget> createState() => _FlashsaleWidgetState();
}

class _FlashsaleWidgetState extends State<FlashsaleWidget> {
  late FlashsaleModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  // Using the global favorites service instance from services/favorites_service.dart

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => FlashsaleModel());
  }

  @override
  void dispose() {
    _model.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          automaticallyImplyLeading: false,
          leading: FlutterFlowIconButton(
            borderRadius: 8.0,
            buttonSize: 40.0,
            icon: Icon(
              Icons.arrow_back_ios_rounded,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 24.0,
            ),
            onPressed: () {
              context.pop();
            },
          ),
          title: Text(
            context.tr('mostSelling', 'Most Selling'),
            style: FlutterFlowTheme.of(context).headlineMedium.override(
                  font: GoogleFonts.interTight(
                    fontWeight: FontWeight.bold,
                    fontStyle:
                        FlutterFlowTheme.of(context).headlineMedium.fontStyle,
                  ),
                  letterSpacing: 0.0,
                  fontWeight: FontWeight.bold,
                  fontStyle:
                      FlutterFlowTheme.of(context).headlineMedium.fontStyle,
                ),
          ),
          actions: const [],
          centerTitle: false,
          elevation: 0.0,
        ),
        body: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 24.0),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      context.tr('trendingNow', 'Trending Now'),
                      style: FlutterFlowTheme.of(context).titleMedium.override(
                            font: GoogleFonts.interTight(
                              fontWeight: FontWeight.w600,
                              fontStyle: FlutterFlowTheme.of(context)
                                  .titleMedium
                                  .fontStyle,
                            ),
                            letterSpacing: 0.0,
                            fontWeight: FontWeight.w600,
                            fontStyle: FlutterFlowTheme.of(context)
                                .titleMedium
                                .fontStyle,
                          ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Icon(
                          Icons.local_fire_department_rounded,
                          color: FlutterFlowTheme.of(context).primary,
                          size: 20.0,
                        ),
                      ].divide(const SizedBox(width: 4.0)),
                    ),
                  ],
                ),
                GridView(
                  padding: EdgeInsets.zero,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16.0,
                    mainAxisSpacing: 16.0,
                    childAspectRatio: 0.7,
                  ),
                  primary: false,
                  shrinkWrap: true,
                  scrollDirection: Axis.vertical,
                  children: [
                    _buildProductItem(
                      product: ProductListItemDTO(
                        id: 'premium-smartphone',
                        name: 'Premium Smartphone',
                        imageUrl:
                            'https://images.unsplash.com/photo-1598327105666-5b89351aff97?q=80&w=1000',
                        price: 899.0,
                        mainPrice: 999.0,
                        featured: false,
                        flashSale: true,
                        inStock: true,
                        rating: 4.7,
                        categories: ['Electronics', 'Smartphones'],
                      ),
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                    _buildProductItem(
                      product: ProductListItemDTO(
                        id: 'wireless-headphones',
                        name: 'Wireless Headphones',
                        imageUrl:
                            'https://images.unsplash.com/photo-1606220588913-b3aacb4d2f46?q=80&w=1000',
                        price: 249.0,
                        mainPrice: 299.0,
                        featured: false,
                        flashSale: true,
                        inStock: true,
                        rating: 4.5,
                        categories: ['Electronics', 'Audio'],
                      ),
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                    _buildProductItem(
                      product: ProductListItemDTO(
                        id: 'running-sneakers',
                        name: 'Running Sneakers',
                        imageUrl:
                            'https://images.unsplash.com/photo-1542291026-7eec264c27ff?q=80&w=1000',
                        price: 129.0,
                        mainPrice: 159.0,
                        featured: false,
                        flashSale: true,
                        inStock: true,
                        rating: 4.2,
                        categories: ['Footwear', 'Sports'],
                      ),
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                    _buildProductItem(
                      product: ProductListItemDTO(
                        id: 'smart-watch',
                        name: 'Smart Watch',
                        imageUrl:
                            'https://images.unsplash.com/photo-1523275335684-37898b6baf30?q=80&w=1000',
                        price: 199.0,
                        mainPrice: 249.0,
                        featured: false,
                        flashSale: true,
                        inStock: false,
                        rating: 4.0,
                        categories: ['Electronics', 'Wearables'],
                      ),
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                    _buildProductItem(
                      product: ProductListItemDTO(
                        id: 'ultrabook-pro',
                        name: 'Ultrabook Pro',
                        imageUrl:
                            'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?q=80&w=1000',
                        price: 1299.0,
                        mainPrice: 1499.0,
                        featured: false,
                        flashSale: true,
                        inStock: true,
                        rating: 4.8,
                        categories: ['Electronics', 'Laptops'],
                      ),
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                    _buildProductItem(
                      product: ProductListItemDTO(
                        id: 'digital-camera',
                        name: 'Digital Camera',
                        imageUrl:
                            'https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?q=80&w=1000',
                        price: 899.0,
                        mainPrice: 1099.0,
                        featured: false,
                        flashSale: true,
                        inStock: true,
                        rating: 4.6,
                        categories: ['Electronics', 'Cameras'],
                      ),
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                  ],
                ),
              ].divide(const SizedBox(height: 24.0)),
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build product items
  Widget _buildProductItem({
    required ProductListItemDTO product,
    required VoidCallback onTap,
  }) {
    // Generate a unique ID for the product based on title
    final String productId = product.name.toLowerCase().replaceAll(' ', '-');
    // Convert price string to double (remove $ and commas)
    final double priceValue = product.price;

    // Check if product is already in favorites
    final bool isInFavorites = favoritesService.isFavorite(productId);

    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: 160,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image with favorite button
              Stack(
                children: [
                  ClipRRect(
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(12)),
                    child: enhancedImageService.buildEnhancedImage(
                      imageUrl: product.imageUrl,
                      width: double.infinity,
                      height: 120,
                      fit: BoxFit.cover,
                      placeholder: Container(
                        width: double.infinity,
                        height: 120,
                        color: Colors.grey[200],
                        child: Center(
                          child: CircularProgressIndicator(
                            color: FlutterFlowTheme.of(context).primary,
                          ),
                        ),
                      ),
                      errorWidget: Container(
                        width: double.infinity,
                        height: 120,
                        color: Colors.grey[200],
                        child: const Icon(Icons.image_not_supported),
                      ),
                    ),
                  ),
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(200),
                        shape: BoxShape.circle,
                      ),
                      child: InkWell(
                        onTap: () async {
                          final bool wasInFavorites = isInFavorites;
                          await favoritesService.toggleFavorite(
                            id: productId,
                            name: product.name,
                            imageUrl: product.imageUrl,
                            description: product.primaryCategory,
                            price: priceValue,
                          );
                          setState(() {});
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  wasInFavorites
                                      ? context.tr('removedFromFavorites', 'Removed from favorites')
                                      : context.tr('addedToFavorites', 'Added to favorites'),
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        font: GoogleFonts.inter(),
                                        color: FlutterFlowTheme.of(context).info,
                                      ),
                                ),
                                duration: const Duration(seconds: 2),
                                backgroundColor:
                                    FlutterFlowTheme.of(context).primary,
                              ),
                            );
                          }
                        },
                        child: Icon(
                          isInFavorites
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: isInFavorites
                              ? Colors.red
                              : FlutterFlowTheme.of(context).secondaryText,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // Product details
              Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            font: GoogleFonts.inter(
                              fontWeight: FontWeight.bold,
                            ),
                            color: Colors.black,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '\$${product.price}',
                      style: FlutterFlowTheme.of(context).titleMedium.override(
                            font: GoogleFonts.inter(
                              fontWeight: FontWeight.bold,
                            ),
                            color: const Color(0xFFFF9B24),
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
